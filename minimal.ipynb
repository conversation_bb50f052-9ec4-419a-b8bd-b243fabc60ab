import warnings
warnings.filterwarnings('ignore')
import numpy as np
import torch
from pathlib import Path
import cv2
from optical_flow_visualizer import flow_to_color, read_flow
import torch.nn as NN
import torch.nn.functional as Fn
import matplotlib.pyplot as plt
import gc


DATASET_PATH = Path('organized_middlebury/Dimetrodon')
CONFIG = {'name': 'baseline', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 0.0, 'pinn_weight': 0.0}
LEARNING_RATE = 1e-4
TV_WEIGHT = 1e-1
TRAIN_EPOCHS = 100 #20000


class FractalDeformationNetwork(NN.Module):
    def __init__(self, input_channels=3, fractal_depth=4, base_channels=32):
        super(FractalDeformationNetwork, self).__init__()
        
        self.fractal_depth = fractal_depth
        self.downsample_layers = NN.ModuleList()
        self.upsample_layers = NN.ModuleList()

        # Encoder: Downsampling layers to create multi-scale features
        current_channels = input_channels
        for _ in range(fractal_depth):
            self.downsample_layers.append(
                NN.Sequential(
                    NN.Conv2d(current_channels, base_channels, kernel_size=3, padding=1),
                    NN.BatchNorm2d(base_channels),
                    NN.ReLU(inplace=True),
                    NN.Conv2d(base_channels, base_channels, kernel_size=3, padding=1),
                    NN.BatchNorm2d(base_channels),
                    NN.ReLU(inplace=True),
                    NN.MaxPool2d(2)
                )
            )
            current_channels = base_channels
            base_channels *= 2
        
        # Decoder: Upsampling layers to reconstruct the fractal structure
        for _ in range(fractal_depth):
            base_channels //= 2
            self.upsample_layers.append(
                NN.Sequential(
                    NN.ConvTranspose2d(current_channels, base_channels, kernel_size=2, stride=2),
                    NN.BatchNorm2d(base_channels),
                    NN.ReLU(inplace=True)
                )
            )
            current_channels = base_channels

    def forward(self, x):
        original_size = x.shape[2:]  # Save original input size

        # Ensure the input is divisible by 16 (because of fractal_depth = 4 and MaxPooling)
        x = Fn.pad(x, [0, (16 - x.shape[3] % 16) % 16, 0, (16 - x.shape[2] % 16) % 16])

        # Encoder: Multi-scale feature extraction
        skip_connections = []
        for down_layer in self.downsample_layers:
            x = down_layer(x)
            skip_connections.append(x)

        # Decoder: Reconstruct features with skip connections
        skip_connections = skip_connections[::-1]  # Reverse for decoder
        for idx, up_layer in enumerate(self.upsample_layers):
            # Upsample
            x = up_layer(x)
            
            # Add skip connection
            skip_connection_resized = Fn.interpolate(
                skip_connections[idx], 
                size=x.shape[2:], 
                mode='bilinear', 
                align_corners=False
            )
            x = x + skip_connection_resized

        # Resize to original input size
        x = Fn.interpolate(x, size=original_size, mode='bilinear', align_corners=False)

        return x

class OpticalFlowNetwork(NN.Module):
    def __init__(self, input_channels=64):
        super(OpticalFlowNetwork, self).__init__()
        self.conv1 = NN.Conv2d(input_channels, 128, kernel_size=3, padding=1)
        self.conv2 = NN.Conv2d(128, 256, kernel_size=3, padding=1)
        self.conv3 = NN.Conv2d(256, 128, kernel_size=3, padding=1)
        self.conv4 = NN.Conv2d(128, 64, kernel_size=3, padding=1)
        self.flow_prediction = NN.Conv2d(64, 2, kernel_size=3, padding=1)  # Predict 2D optical flow

    def forward(self, x):
        x = Fn.relu(self.conv1(x))
        x = Fn.relu(self.conv2(x))
        x = Fn.relu(self.conv3(x))
        x = Fn.relu(self.conv4(x))
        flow = self.flow_prediction(x)
        return flow

class OpticalFlowWithFDN(NN.Module):
    def __init__(self, input_channels=2):  # 2 because we input 2 grayscale images (1 channel each)
        super(OpticalFlowWithFDN, self).__init__()
        self.fdn = FractalDeformationNetwork(input_channels=input_channels)

        # Projection layer to adjust the output of FDN to 64 channels
        self.projection_layer = NN.Conv2d(32, 64, kernel_size=1)  # Project 32 channels to 64

        self.optical_flow_net = OpticalFlowNetwork()

    def forward(self, input_pair):
        # Extract fractal features using FDN
        fractal_features = self.fdn(input_pair)
        
        # Project fractal features to 64 channels
        fractal_features = self.projection_layer(fractal_features)
        
        # Predict optical flow based on fractal features
        flow = self.optical_flow_net(fractal_features)
        
        return flow


def load_and_preprocess_image(file_path):
    image = cv2.imread(str(file_path), cv2.IMREAD_GRAYSCALE)
    image = image.astype(np.float32) / 255.0
    image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0)
    return image_tensor.cuda()

def create_grid(height, width):
    x = torch.linspace(-1, 1, width)
    y = torch.linspace(-1, 1, height)
    grid_y, grid_x = torch.meshgrid(y, x, indexing='ij')
    grid = torch.stack((grid_x, grid_y), dim=-1)
    return grid

def warp(img, flow):
    B, C, H, W = img.size()
    
    # Create grid in normalized coordinates (-1 to 1)
    grid = create_grid(H, W).cuda()
    grid = grid.unsqueeze(0).expand(B, H, W, 2)
    
    # Transform flow from pixel to normalized coordinates
    flow = flow.permute(0, 2, 3, 1)
    flow = flow / torch.tensor([(W-1)/2, (H-1)/2]).cuda()
    
    warped_grid = grid + flow
    warped = Fn.grid_sample(img, warped_grid, mode='bilinear', padding_mode='border', align_corners=True)
    return warped


def compute_tv1_loss(flow):
    # anIsotropic TV (current)
    flow_dx = torch.abs(flow[:, :, :, 1:] - flow[:, :, :, :-1])
    flow_dy = torch.abs(flow[:, :, 1:, :] - flow[:, :, :-1, :])
    return flow_dx.mean() + flow_dy.mean()

# def compute_pinn_loss(flow, img2, img1):
#     """Physics-Informed Neural Network loss"""
#     warped_next = warp(img2, flow)
#     brightness_error = torch.abs(warped_next - img1)
    
#     def compute_gradients(img):
#         dx = Fn.pad(img[:, :, :, 1:] - img[:, :, :, :-1], (0, 1, 0, 0))
#         dy = Fn.pad(img[:, :, 1:, :] - img[:, :, :-1, :], (0, 0, 0, 1))
#         return dx, dy
    
#     img2_dx, img2_dy = compute_gradients(img2)
#     img1_dx, img1_dy = compute_gradients(img1)
    
#     warped_dx = warp(img2_dx, flow)
#     warped_dy = warp(img2_dy, flow)
    
#     grad_error = torch.abs(warped_dx - img1_dx) + torch.abs(warped_dy - img1_dy) #l1
    
#     return brightness_error.mean() + 0.5 * grad_error.mean()

# def compute_total_loss(current_img, next_img, flow, tv_weight, pinn_weight, l1_weight=0.2, l2_weight=0.8):
def compute_total_loss(current_img, next_img, flow,  l1_weight=0.2, l2_weight=0.8):
    # L1 loss
    l1_data_loss = Fn.l1_loss(warp(next_img, flow), current_img)
    
    # L2 loss
    l2_data_loss = Fn.mse_loss(warp(next_img, flow), current_img)
    
    # Combined data loss with weights
    data_loss = l1_weight * l1_data_loss + l2_weight * l2_data_loss
    # return data_loss
    
    # TV loss
    tv_loss = compute_tv1_loss(flow)
    
    # # PINN loss
    # pinn_loss = compute_pinn_loss(flow, next_img, current_img)
    
    # Total loss
    # total_loss = data_loss + tv_weight * tv_loss + pinn_weight * pinn_loss
    total_loss = data_loss + TV_WEIGHT * tv_loss
    return total_loss
    
    # return total_loss, {
    #     'data_loss': data_loss.item(),
    #     'l1_data_loss': l1_data_loss.item(),
    #     'l2_data_loss': l2_data_loss.item(),
    #     'tv_loss': tv_loss.item(),
    #     'pinn_loss': pinn_loss.item() 
    # }



current_img = load_and_preprocess_image(DATASET_PATH / 'frame10.png')
next_img = load_and_preprocess_image(DATASET_PATH / 'frame11.png')
# ground_truth_flow = torch.from_numpy(read_flow(DATASET_PATH / 'flow10.flo')).permute(2, 0, 1).unsqueeze(0).cuda()
model = OpticalFlowWithFDN(input_channels=2).cuda()
optimizer = torch.optim.Adam(model.parameters(), lr=LEARNING_RATE)
input_pair = torch.cat((current_img, next_img), dim=1)
best_loss = float('inf')
best_metrics = {
    'aee': float('inf'),
    'sdee': float('inf'),
    'aae': float('inf'),
    'sdae': float('inf')
}
best_loss_epoch = 0
loss_history = []
metrics_history = []

# Start timing
# start_time = datetime.datetime.now()
best_epoch_time = None





try:
    for epoch in range(TRAIN_EPOCHS):
        # epoch_start_time = datetime.datetime.now()
        
        model.train()
        optimizer.zero_grad()
        
        # # with torch.autocast(device_type='cuda'):
        predicted_flow = model(input_pair)
        # # loss, loss_metrics = compute_total_loss(
        # #     current_img, next_img, predicted_flow,
        # #     config['tv_weight'], config['pinn_weight']
        # # )

        loss = compute_total_loss(current_img, next_img, predicted_flow)



        # # Only compute metrics during evaluation (not used for training)
        # with torch.no_grad():
        #     aee, sdee, aae, sdae = calculate_metrics(
        #         predicted_flow.squeeze(0).permute(1, 2, 0),
        #         ground_truth_flow.squeeze(0).permute(1, 2, 0)
        #     )
        
        loss.backward()
        optimizer.step()


        
        loss_history.append(loss.item())
        # metrics_history.append({
        #     'aee': aee,
        #     'sdee': sdee,
        #     'aae': aae,
        #     'sdae': sdae
        # })
        
        # Save best loss model
        if loss.item() < best_loss:
            best_loss = loss.item()
            best_loss_epoch = epoch
            # best_metrics = {
            #     'aee': aee,
            #     'sdee': sdee,
            #     'aae': aae,
            #     'sdae': sdae
            # }
            # best_epoch_time = datetime.datetime.now()
            
            # Calculate time to reach best epoch
            # time_to_best = (best_epoch_time - start_time).total_seconds()
            
            if epoch % 10 == 0:
                print('epoch: ', epoch,'best loss: ', best_loss)

            # torch.save({
            #     'epoch': epoch,
            #     'model_state_dict': model.state_dict(),
            #     'optimizer_state_dict': optimizer.state_dict(),
            #     'loss': loss.item(),
            #     'metrics': best_metrics,
            #     'config': config,
            #     'time_to_best': time_to_best
            # }, log_dir / f"{config['name']}_best_loss_model.pth")
            
            # logger.info(f"New best loss: {best_loss:.2e} at epoch {best_loss_epoch}")
            # logger.info(f"Time to reach best epoch: {time_to_best:.2f} seconds")
            # logger.info(f"  AEE: {best_metrics['aee']:.2e}, SDEE: {best_metrics['sdee']:.2e}")
            # logger.info(f"  AAE: {best_metrics['aae']:.2e}, SDAE: {best_metrics['sdae']:.2e}")
            
            # # Plot flow visualization when we get a better loss
            # plot_flow(
            #     current_img, ground_truth_flow, predicted_flow,
            #     epoch, aee, loss.item(),
            #     log_dir / f"{config['name']}_best_flow_epoch_{epoch}.png"
            # )
        
        # if epoch % 2 == 0:
        #     epoch_time = (datetime.datetime.now() - epoch_start_time).total_seconds()
        #     logger.info(f"\nEpoch {epoch}:")
        #     logger.info(f"  Loss: {loss.item():.2e}")
        #     logger.info(f"  AEE: {aee:.2e}, SDEE: {sdee:.2e}")
        #     logger.info(f"  AAE: {aae:.2e}, SDAE: {sdae:.2e}")
        #     logger.info(f"  Best Loss: {best_loss:.2e} (Epoch {best_loss_epoch})")
        #     logger.info(f"  Epoch Time: {epoch_time:.2f} seconds")
        
        # Clear memory after each epoch
        if epoch != TRAIN_EPOCHS - 1:
            del predicted_flow, loss #, loss_metrics
            torch.cuda.empty_cache()
            gc.collect()
except KeyboardInterrupt:
    print("Training interrupted by user")


# Plot loss history
# plt.subplot(221)
plt.plot(loss_history, 'b-', label='Loss')
plt.scatter([best_loss_epoch], [loss_history[best_loss_epoch]], 
            color='red', s=100, label=f'Best Loss: {best_loss:.6e}')
plt.title('Loss History')
plt.xlabel('Epoch')
plt.ylabel('Loss')
plt.yscale('log')
plt.legend()
plt.grid(True)
