# Binary Search Hyperparameter Optimization Guide

## Overview
Use **5-point binary search** for continuous hyperparameters and **grid search** for discrete parameters.

## Parameter Categories

### 🔍 Binary Search (Continuous Parameters)
1. **TV Weight (λ_tv)** - [0, 1e-1]
2. **PINN Weight (λ_pinn)** - [0, 1e-2] 
3. **Learning Rate** - [1e-5, 1e-3]
4. **L1 Weight** - [0, 1]
5. **L2 Weight** - [0, 1]

### 📊 Grid Search (Discrete Parameters)
1. **Fractal Depth** - [2, 3, 4, 5, 6]
2. **Base Channels** - [16, 32, 64, 128]
3. **Optimizer Type** - [<PERSON>, <PERSON>G<PERSON>, <PERSON><PERSON>]

### 🔒 Fixed Parameters
- **Interpolation Mode** = 'bilinear'
- **Padding Mode** = 'border'

## 5-Point Binary Search Strategy

### Why 5 Points?
```
Test 5 points to determine search direction:
- Low: λ_tv = 0
- Low-Mid: λ_tv = 2.5e-2  
- Mid: λ_tv = 5e-2
- High-Mid: λ_tv = 7.5e-2
- High: λ_tv = 1e-1

If best is at Mid: search [Low-Mid, High-Mid]
If best is at Low-Mid: search [Low, Mid]
If best is at High-Mid: search [Mid, High]
```

### Recursive Bisection Implementation
```python
def recursive_bisection_search(param_name, low_val, high_val, max_iterations=5):
    """
    Recursive bisection search - like fractal, keeps dividing space
    """
    best_value = None
    best_aee = float('inf')
    
    for iteration in range(max_iterations):
        # Calculate 5 points in current range
        points = {
            'low': low_val,
            'low_mid': low_val + 0.25 * (high_val - low_val),
            'mid': low_val + 0.5 * (high_val - low_val),
            'high_mid': low_val + 0.75 * (high_val - low_val),
            'high': high_val
        }
        
        # Test all 5 points
        results = {}
        for name, value in points.items():
            config = create_config(param_name, value)
            result = train_and_evaluate(config)
            results[name] = result['aee']
            
            # Update best if better
            if result['aee'] < best_aee:
                best_aee = result['aee']
                best_value = value
        
        # Find best point in this iteration
        best_point = min(results, key=results.get)
        
        # Update search range for next iteration (recursive bisection)
        if best_point == 'mid':
            # Best is in middle - search around it
            low_val = points['low_mid']
            high_val = points['high_mid']
        elif best_point in ['low', 'low_mid']:
            # Best is in lower half
            low_val = points['low']
            high_val = points['mid']
        else:  # high or high_mid
            # Best is in upper half
            low_val = points['mid']
            high_val = points['high']
        
        print(f"Iteration {iteration+1}: Best {best_point} = {points[best_point]:.6f}, AEE = {results[best_point]:.6f}")
        print(f"New range: [{low_val:.6f}, {high_val:.6f}]")
    
    return best_value, best_aee
```

### Example: TV Weight Bisection
```
Initial Range: [0, 0.1]

Iteration 1 - Test 5 points:
- 0.000000 (low)
- 0.025000 (low_mid) 
- 0.050000 (mid) ← BEST
- 0.075000 (high_mid)
- 0.100000 (high)

New Range: [0.025, 0.075]

Iteration 2 - Test 5 points in new range:
- 0.025000 (low)
- 0.037500 (low_mid) ← BEST
- 0.050000 (mid)
- 0.062500 (high_mid)
- 0.075000 (high)

New Range: [0.025, 0.05]

Iteration 3 - Continue bisecting...
```

## Search Ranges

### TV Weight (λ_tv)
- **Range**: [0, 1e-1]
- **5 Points**: [0, 2.5e-2, 5e-2, 7.5e-2, 1e-1]
- **Expected Optimal**: 1e-3 to 1e-2

### PINN Weight (λ_pinn) - Gradient Consistency
- **Range**: [0, 1e-2]
- **5 Points**: [0, 2.5e-3, 5e-3, 7.5e-3, 1e-2]
- **Expected Optimal**: 1e-4 to 1e-3

### Learning Rate
- **Range**: [1e-5, 1e-3]
- **5 Points**: [1e-5, 2.5e-4, 5e-4, 7.5e-4, 1e-3]
- **Expected Optimal**: 5e-5 to 5e-4

## Complete Optimization Workflow

### Step 1: Grid Search (Discrete)
```python
# Test each discrete value
for depth in [2, 3, 4, 5, 6]:
    for channels in [16, 32, 64, 128]:
        for optimizer in ['Adam', 'SGD', 'AdamW']:
            # Test configuration
```

### Step 2: 5-Point Binary Search (Continuous)
```python
# Use best discrete config, then 5-point binary search
best_lr, _, (lr_low, lr_high) = five_point_binary_search('learning_rate', 1e-5, 1e-3)
best_tv, _, (tv_low, tv_high) = five_point_binary_search('tv_weight', 0, 1e-1)
best_pinn, _, (pinn_low, pinn_high) = five_point_binary_search('pinn_weight', 0, 1e-2)
```

### Step 3: Fine-tune Combinations
```python
# Test combinations of best individual parameters
configs = [
    {'tv_weight': best_tv, 'pinn_weight': 0},
    {'tv_weight': 0, 'pinn_weight': best_pinn},
    {'tv_weight': best_tv, 'pinn_weight': best_pinn},
    {'tv_weight': best_tv/2, 'pinn_weight': best_pinn/2}
]
``` 