import warnings
warnings.filterwarnings('ignore')
import matplotlib
matplotlib.use('Agg')  # Set the backend to non-interactive Agg
import torch
import torch.nn as NN
import torch.nn.functional as Fn
import cv2
from pathlib import Path
import numpy as np
import matplotlib.pyplot as plt
from optical_flow_visualizer import flow_to_color, read_flow
import logging
import datetime
import json
import gc
import os

# Setup logging
def setup_logging(experiment_name, base_dir=None):
    # Create a directory structure based on configuration name
    if base_dir is None:
        log_dir = Path('logs') / experiment_name
    else:
        log_dir = base_dir
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # File handler
    log_file = log_dir / 'training.log'
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.INFO)
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # Formatter
    formatter = logging.Formatter('%(asctime)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # Logger setup
    logger = logging.getLogger(experiment_name)
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger, log_dir

# Hyperparameter configurations to compare
configs = [
    # Baseline (no regularization)
    {'name': 'baseline', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 0.0, 'pinn_weight': 0.0},
    
    # TV regularization configurations
    {'name': 'tv1e-1', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-1, 'pinn_weight': 0.0},
    # {'name': 'tv1e-2', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-2, 'pinn_weight': 0.0},
    # {'name': 'tv1e-3', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-3, 'pinn_weight': 0.0},
    
    #{'name': 'tv1e-4', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-4, 'pinn_weight': 0.0},
    # {'name': 'tv1e-5', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-5, 'pinn_weight': 0.0},
    # {'name': 'tv1e-6', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-6, 'pinn_weight': 0.0},
    
    # Commented out other configurations
    # {'name': 'pinn1e-4', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 0.0, 'pinn_weight': 1e-4},
    # {'name': 'pinn1e-5', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 0.0, 'pinn_weight': 1e-5},
    # {'name': 'pinn1e-6', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 0.0, 'pinn_weight': 1e-6},
    # {'name': 'tv1e-4_pinn1e-4', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-4, 'pinn_weight': 1e-4},
    # {'name': 'tv1e-5_pinn1e-5', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-5, 'pinn_weight': 1e-5},
    # {'name': 'tv1e-6_pinn1e-6', 'learning_rate': 1e-4, 'fractal_depth': 4, 'tv_weight': 1e-6, 'pinn_weight': 1e-6},
    # {'name': 'lr1e-3', 'learning_rate': 1e-3, 'fractal_depth': 4, 'tv_weight': 0.0, 'pinn_weight': 0.0},
    # {'name': 'lr1e-5', 'learning_rate': 1e-5, 'fractal_depth': 4, 'tv_weight': 0.0, 'pinn_weight': 0.0},
    # {'name': 'depth3', 'learning_rate': 1e-4, 'fractal_depth': 3, 'tv_weight': 0.0, 'pinn_weight': 0.0},
    # {'name': 'depth5', 'learning_rate': 1e-4, 'fractal_depth': 5, 'tv_weight': 0.0, 'pinn_weight': 0.0},
]

# Update the base directory for synthetic data
BASE_DIR = Path('./flow_visualization')
TRAIN_EPOCHS = 10 #20000

# Fractal Deformation Network (FDN) module
class FractalDeformationNetwork(NN.Module):
    def __init__(self, input_channels=3, fractal_depth=4, base_channels=32):
        super(FractalDeformationNetwork, self).__init__()
        
        self.fractal_depth = fractal_depth
        self.downsample_layers = NN.ModuleList()
        self.upsample_layers = NN.ModuleList()

        # Encoder: Downsampling layers to create multi-scale features
        current_channels = input_channels
        for _ in range(fractal_depth):
            self.downsample_layers.append(
                NN.Sequential(
                    NN.Conv2d(current_channels, base_channels, kernel_size=3, padding=1),
                    NN.BatchNorm2d(base_channels),
                    NN.ReLU(inplace=True),
                    NN.Conv2d(base_channels, base_channels, kernel_size=3, padding=1),
                    NN.BatchNorm2d(base_channels),
                    NN.ReLU(inplace=True),
                    NN.MaxPool2d(2)
                )
            )
            current_channels = base_channels
            base_channels *= 2
        
        # Decoder: Upsampling layers to reconstruct the fractal structure
        for _ in range(fractal_depth):
            base_channels //= 2
            self.upsample_layers.append(
                NN.Sequential(
                    NN.ConvTranspose2d(current_channels, base_channels, kernel_size=2, stride=2),
                    NN.BatchNorm2d(base_channels),
                    NN.ReLU(inplace=True)
                )
            )
            current_channels = base_channels

    def forward(self, x):
        original_size = x.shape[2:]  # Save original input size

        # Ensure the input is divisible by 16 (because of fractal_depth = 4 and MaxPooling)
        x = Fn.pad(x, [0, (16 - x.shape[3] % 16) % 16, 0, (16 - x.shape[2] % 16) % 16])

        # Encoder: Multi-scale feature extraction
        skip_connections = []
        for down_layer in self.downsample_layers:
            x = down_layer(x)
            skip_connections.append(x)

        # Decoder: Reconstruct features with skip connections
        skip_connections = skip_connections[::-1]  # Reverse for decoder
        for idx, up_layer in enumerate(self.upsample_layers):
            # Upsample
            x = up_layer(x)
            
            # Add skip connection
            skip_connection_resized = Fn.interpolate(
                skip_connections[idx], 
                size=x.shape[2:], 
                mode='bilinear', 
                align_corners=False
            )
            x = x + skip_connection_resized

        # Resize to original input size
        x = Fn.interpolate(x, size=original_size, mode='bilinear', align_corners=False)

        return x

# Optical Flow Prediction Network (CNN)
class OpticalFlowNetwork(NN.Module):
    def __init__(self, input_channels=64):
        super(OpticalFlowNetwork, self).__init__()
        self.conv1 = NN.Conv2d(input_channels, 128, kernel_size=3, padding=1)
        self.conv2 = NN.Conv2d(128, 256, kernel_size=3, padding=1)
        self.conv3 = NN.Conv2d(256, 128, kernel_size=3, padding=1)
        self.conv4 = NN.Conv2d(128, 64, kernel_size=3, padding=1)
        self.flow_prediction = NN.Conv2d(64, 2, kernel_size=3, padding=1)  # Predict 2D optical flow

    def forward(self, x):
        x = Fn.relu(self.conv1(x))
        x = Fn.relu(self.conv2(x))
        x = Fn.relu(self.conv3(x))
        x = Fn.relu(self.conv4(x))
        flow = self.flow_prediction(x)
        return flow

# Complete model combining FDN and Optical Flow Network
class OpticalFlowWithFDN(NN.Module):
    def __init__(self, input_channels=2):  # 2 because we input 2 grayscale images (1 channel each)
        super(OpticalFlowWithFDN, self).__init__()
        self.fdn = FractalDeformationNetwork(input_channels=input_channels)

        # Projection layer to adjust the output of FDN to 64 channels
        self.projection_layer = NN.Conv2d(32, 64, kernel_size=1)  # Project 32 channels to 64

        self.optical_flow_net = OpticalFlowNetwork()

    def forward(self, input_pair):
        # Extract fractal features using FDN
        fractal_features = self.fdn(input_pair)
        
        # Project fractal features to 64 channels
        fractal_features = self.projection_layer(fractal_features)
        
        # Predict optical flow based on fractal features
        flow = self.optical_flow_net(fractal_features)
        
        return flow

def create_grid(height, width):
    x = torch.linspace(-1, 1, width)
    y = torch.linspace(-1, 1, height)
    grid_y, grid_x = torch.meshgrid(y, x, indexing='ij')
    grid = torch.stack((grid_x, grid_y), dim=-1)
    return grid

def warp(img, flow):
    B, C, H, W = img.size()
    
    # Create grid in normalized coordinates (-1 to 1)
    grid = create_grid(H, W).cuda()
    grid = grid.unsqueeze(0).expand(B, H, W, 2)
    
    # Transform flow from pixel to normalized coordinates
    flow = flow.permute(0, 2, 3, 1)
    flow = flow / torch.tensor([(W-1)/2, (H-1)/2]).cuda()
    
    warped_grid = grid + flow
    warped = Fn.grid_sample(img, warped_grid, mode='bilinear', padding_mode='border', align_corners=True)
    return warped

def compute_tv1_loss(flow):
    # anIsotropic TV (current)
    flow_dx = torch.abs(flow[:, :, :, 1:] - flow[:, :, :, :-1])
    flow_dy = torch.abs(flow[:, :, 1:, :] - flow[:, :, :-1, :])
    return flow_dx.mean() + flow_dy.mean()

def compute_pinn_loss(flow, img2, img1):
    """Physics-Informed Neural Network loss"""
    warped_next = warp(img2, flow)
    brightness_error = torch.abs(warped_next - img1)
    
    def compute_gradients(img):
        dx = Fn.pad(img[:, :, :, 1:] - img[:, :, :, :-1], (0, 1, 0, 0))
        dy = Fn.pad(img[:, :, 1:, :] - img[:, :, :-1, :], (0, 0, 0, 1))
        return dx, dy
    
    img2_dx, img2_dy = compute_gradients(img2)
    img1_dx, img1_dy = compute_gradients(img1)
    
    warped_dx = warp(img2_dx, flow)
    warped_dy = warp(img2_dy, flow)
    
    grad_error = torch.abs(warped_dx - img1_dx) + torch.abs(warped_dy - img1_dy) #l1
    
    return brightness_error.mean() + 0.5 * grad_error.mean()

def compute_total_loss(current_img, next_img, flow, tv_weight, pinn_weight, l1_weight=0.2, l2_weight=0.8):
    # L1 loss
    l1_data_loss = Fn.l1_loss(warp(next_img, flow), current_img)
    
    # L2 loss
    l2_data_loss = Fn.mse_loss(warp(next_img, flow), current_img)
    
    # Combined data loss with weights
    data_loss = l1_weight * l1_data_loss + l2_weight * l2_data_loss
    
    # TV loss
    tv_loss = compute_tv1_loss(flow)
    
    # PINN loss
    pinn_loss = compute_pinn_loss(flow, next_img, current_img)
    
    # Total loss
    total_loss = data_loss + tv_weight * tv_loss + pinn_weight * pinn_loss
    
    return total_loss, {
        'data_loss': data_loss.item(),
        'l1_data_loss': l1_data_loss.item(),
        'l2_data_loss': l2_data_loss.item(),
        'tv_loss': tv_loss.item(),
        'pinn_loss': pinn_loss.item() 
    }

def calculate_metrics(pred_flow, gt_flow):
    """Calculate both endpoint error and angular error metrics"""
    if torch.is_tensor(pred_flow):
        pred_flow = pred_flow.detach().cpu().numpy()
    if torch.is_tensor(gt_flow):
        gt_flow = gt_flow.detach().cpu().numpy()
    
    # Extract u,v components from predicted and ground truth flows
    u = pred_flow[..., 0]  # First channel is u
    v = pred_flow[..., 1]  # Second channel is v
    uGT = gt_flow[..., 0]  # Ground truth u
    vGT = gt_flow[..., 1]  # Ground truth v
    
    # Calculate endpoint error
    _EE = np.sqrt((u - uGT)**2 + (v - vGT)**2)
    _EE_ignore = _EE[_EE <= 50]
    
    if len(_EE_ignore) > 0:
        AEE = np.mean(_EE_ignore)
        SDEE = np.sqrt(np.mean((_EE_ignore - AEE)**2))  # Standard deviation
    else:
        AEE = float('inf')
        SDEE = float('inf')
    
    # Calculate angular error
    # Add 1 to handle zero flow case
    numerator = u * uGT + v * vGT + 1
    denominator = np.sqrt((u**2 + v**2 + 1) * (uGT**2 + vGT**2 + 1))
    # Clip to avoid numerical issues
    cos_angle = np.clip(numerator / denominator, -1.0, 1.0)
    _AE = np.arccos(cos_angle) 
    
    # Filter out invalid values
    _AE_valid = _AE[~np.isnan(_AE) & ~np.isinf(_AE)]
    
    if len(_AE_valid) > 0:
        AAE = np.mean(_AE_valid)
        SDAE = np.sqrt(np.mean((_AE_valid - AAE)**2))  # Standard deviation
    else:
        AAE = float('inf')
        SDAE = float('inf')
    
    return AEE, SDEE, AAE, SDAE

def plot_flow(current_img, ground_truth_flow, predicted_flow, epoch, aee, loss, save_path):
    pred_flow = predicted_flow.squeeze(0).permute(1, 2, 0).cpu().detach().numpy()
    gt_flow = ground_truth_flow.squeeze(0).permute(1, 2, 0).cpu().numpy()
    
    invalid_mask = np.isnan(pred_flow[:,:,0]) | np.isnan(pred_flow[:,:,1]) | \
                  np.isinf(pred_flow[:,:,0]) | np.isinf(pred_flow[:,:,1])
    pred_flow[invalid_mask] = 0

    plt.figure(figsize=(10, 5))
    
    plt.subplot(121)
    plt.imshow(flow_to_color(gt_flow))
    plt.title('Ground Truth Flow')
    plt.axis('off')
    
    plt.subplot(122)
    plt.imshow(flow_to_color(pred_flow))
    plt.title(f'Predicted Flow\nEpoch {epoch}\nAEE: {aee:.2f}, Loss: {loss:.6f}')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(save_path)
    plt.close()

def load_and_preprocess_image(file_path):
    # Handle both .png and .npy files
    if file_path.suffix == '.npy':
        image = np.load(str(file_path))
    else:
        image = cv2.imread(str(file_path), cv2.IMREAD_GRAYSCALE)
    image = image.astype(np.float32) / 255.0
    image_tensor = torch.from_numpy(image).unsqueeze(0).unsqueeze(0)
    return image_tensor.cuda()

def process_dataset(dataset_path, config, logger, log_dir):
    """Process a single dataset and return results"""
    logger.info(f"\nProcessing dataset: {dataset_path.name}")
    
    # Load data
    current_img = load_and_preprocess_image(dataset_path / 'frame10.png')
    next_img = load_and_preprocess_image(dataset_path / 'frame11.png')
    ground_truth_flow = torch.from_numpy(read_flow(dataset_path / 'flow10.flo')).permute(2, 0, 1).unsqueeze(0).cuda()
    
    # Initialize model
    model = OpticalFlowWithFDN(input_channels=2).cuda()
    optimizer = torch.optim.Adam(model.parameters(), lr=config['learning_rate'])
    
    # Training setup
    input_pair = torch.cat((current_img, next_img), dim=1)
    best_loss = float('inf')
    best_metrics = {
        'aee': float('inf'),
        'sdee': float('inf'),
        'aae': float('inf'),
        'sdae': float('inf')
    }
    best_loss_epoch = 0
    loss_history = []
    metrics_history = []
    
    # Start timing
    start_time = datetime.datetime.now()
    best_epoch_time = None
    
    try:
        for epoch in range(TRAIN_EPOCHS):
            epoch_start_time = datetime.datetime.now()
            
            model.train()
            optimizer.zero_grad()
            
            predicted_flow = model(input_pair)
            loss, loss_metrics = compute_total_loss(
                current_img, next_img, predicted_flow,
                config['tv_weight'], config['pinn_weight']
            )
            
            # Only compute metrics during evaluation (not used for training)
            with torch.no_grad():
                aee, sdee, aae, sdae = calculate_metrics(
                    predicted_flow.squeeze(0).permute(1, 2, 0),
                    ground_truth_flow.squeeze(0).permute(1, 2, 0)
                )
            
            loss.backward()
            optimizer.step()
            
            loss_history.append(loss.item())
            metrics_history.append({
                'aee': aee,
                'sdee': sdee,
                'aae': aae,
                'sdae': sdae
            })
            
            # Save best loss model
            if loss.item() < best_loss:
                best_loss = loss.item()
                best_loss_epoch = epoch
                best_metrics = {
                    'aee': aee,
                    'sdee': sdee,
                    'aae': aae,
                    'sdae': sdae
                }
                best_epoch_time = datetime.datetime.now()
                
                # Calculate time to reach best epoch
                time_to_best = (best_epoch_time - start_time).total_seconds()
                
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': loss.item(),
                    'metrics': best_metrics,
                    'config': config,
                    'time_to_best': time_to_best
                }, log_dir / f"{config['name']}_best_loss_model.pth")
                
                logger.info(f"New best loss: {best_loss:.2e} at epoch {best_loss_epoch}")
                logger.info(f"Time to reach best epoch: {time_to_best:.2f} seconds")
                logger.info(f"  AEE: {best_metrics['aee']:.2e}, SDEE: {best_metrics['sdee']:.2e}")
                logger.info(f"  AAE: {best_metrics['aae']:.2e}, SDAE: {best_metrics['sdae']:.2e}")
                
                # Plot flow visualization when we get a better loss
                plot_flow(
                    current_img, ground_truth_flow, predicted_flow,
                    epoch, aee, loss.item(),
                    log_dir / f"{config['name']}_best_flow_epoch_{epoch}.png"
                )
            
            if epoch % 2 == 0:
                epoch_time = (datetime.datetime.now() - epoch_start_time).total_seconds()
                logger.info(f"\nEpoch {epoch}:")
                logger.info(f"  Loss: {loss.item():.2e}")
                logger.info(f"  AEE: {aee:.2e}, SDEE: {sdee:.2e}")
                logger.info(f"  AAE: {aae:.2e}, SDAE: {sdae:.2e}")
                logger.info(f"  Best Loss: {best_loss:.2e} (Epoch {best_loss_epoch})")
                logger.info(f"  Epoch Time: {epoch_time:.2f} seconds")
            
            # Clear memory after each epoch
            if epoch != TRAIN_EPOCHS - 1:
                del predicted_flow, loss, loss_metrics
                torch.cuda.empty_cache()
                gc.collect()
    except KeyboardInterrupt:
        logger.info("\nTraining interrupted by user")
    
    # Calculate total training time
    end_time = datetime.datetime.now()
    training_time = (end_time - start_time).total_seconds()
    
    # Log final results with timing information
    logger.info("\nFinal Results:")
    logger.info(f"Total Training Time: {training_time:.2f} seconds")
    if best_epoch_time:
        time_to_best = (best_epoch_time - start_time).total_seconds()
        logger.info(f"Time to reach best epoch: {time_to_best:.2f} seconds")
    logger.info(f"Best Loss: {best_loss:.2e} at epoch {best_loss_epoch}")
    logger.info(f"Best Metrics:")
    logger.info(f"  AEE: {best_metrics['aee']:.2e}, SDEE: {best_metrics['sdee']:.2e}")
    logger.info(f"  AAE: {best_metrics['aae']:.2e}, SDAE: {best_metrics['sdae']:.2e}")
    
    # Plot final results
    plt.figure(figsize=(15, 10))
    
    # Plot loss history
    plt.subplot(221)
    plt.plot(loss_history, 'b-', label='Loss')
    plt.scatter([best_loss_epoch], [loss_history[best_loss_epoch]], 
               color='red', s=100, label=f'Best Loss: {best_loss:.6e}')
    plt.title('Loss History')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.yscale('log')
    plt.legend()
    plt.grid(True)
    
    # Plot AEE history
    plt.subplot(222)
    aee_history = [m['aee'] for m in metrics_history]
    plt.plot(aee_history, 'g-', label='AEE')
    plt.scatter([best_loss_epoch], [aee_history[best_loss_epoch]], 
               color='red', s=100, label=f'Best AEE: {best_metrics["aee"]:.6e}')
    plt.title('AEE History')
    plt.xlabel('Epoch')
    plt.ylabel('AEE')
    plt.legend()
    plt.grid(True)
    
    # Plot AAE history
    plt.subplot(223)
    aae_history = [m['aae'] for m in metrics_history]
    plt.plot(aae_history, 'r-', label='AAE')
    plt.scatter([best_loss_epoch], [aae_history[best_loss_epoch]], 
               color='red', s=100, label=f'Best AAE: {best_metrics["aae"]:.6e}')
    plt.title('AAE History')
    plt.xlabel('Epoch')
    plt.ylabel('AAE')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(log_dir / f"{config['name']}_final_results.png", dpi=300, bbox_inches='tight')
    plt.close()
    
    return {
        'config': config,
        'best_loss': best_loss,
        'best_loss_epoch': best_loss_epoch,
        'best_metrics': best_metrics,
        'training_time': training_time,
        'time_to_best': time_to_best if best_epoch_time else None
    }

def main():
    # Set random seeds for reproducibility
    seed = 42
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    np.random.seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    # Base directory containing all datasets
    base_dir = Path('organized_middlebury')
    
    # Create main experiment directory with timestamp
    timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
    main_log_dir = Path('experiments_middlebury') / timestamp
    main_log_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize main logger
    main_logger, _ = setup_logging('main', base_dir=main_log_dir)
    
    # Save configurations
    with open(main_log_dir / 'configs.txt', 'w') as f:
        f.write("Configuration Parameters:\n")
        f.write("=" * 80 + "\n")
        for config in configs:
            f.write(f"\nConfiguration: {config['name']}\n")
            f.write("-" * 40 + "\n")
            for key, value in config.items():
                f.write(f"{key}: {value}\n")
    
    # Process each dataset
    all_results = {}
    
    for dataset_dir in base_dir.iterdir():
        if not dataset_dir.is_dir() or dataset_dir.name != 'Dimetrodon':
            continue
            
        dataset_name = dataset_dir.name
        main_logger.info(f"\nProcessing dataset: {dataset_name}")
        
        # Create dataset-specific directory
        dataset_log_dir = main_log_dir / dataset_name
        dataset_log_dir.mkdir(exist_ok=True)
        
        dataset_results = []
        
        # Process each configuration for this dataset
        for config in configs:
            config_dir = dataset_log_dir / config['name']
            config_dir.mkdir(exist_ok=True)
            
            logger, log_dir = setup_logging(f"{dataset_name}_{config['name']}", base_dir=config_dir)
            result = process_dataset(dataset_dir, config, logger, log_dir)
            dataset_results.append(result)
            
            # Save individual dataset results
            with open(config_dir / 'results.txt', 'w') as f:
                f.write(f"Results for {dataset_name} - {config['name']}\n")
                f.write("=" * 80 + "\n")
                f.write(f"Best Loss: {result['best_loss']:.7f}\n")
                f.write(f"Best Epoch: {result['best_loss_epoch']}\n")
                f.write(f"Best Metrics:\n")
                f.write(f"  AEE: {result['best_metrics']['aee']:.4f}\n")
                f.write(f"  SDEE: {result['best_metrics']['sdee']:.4f}\n")
                f.write(f"  AAE: {result['best_metrics']['aae']:.4f}\n")
                f.write(f"  SDAE: {result['best_metrics']['sdae']:.4f}\n")
                f.write(f"Training Time: {result['training_time']:.2f} seconds\n")
                if result['time_to_best']:
                    f.write(f"Time to reach best epoch: {result['time_to_best']:.2f} seconds\n")
        
        all_results[dataset_name] = dataset_results
    
    # Save summary of all results
    with open(main_log_dir / 'all_results_summary.txt', 'w') as f:
        f.write("Summary of All Results\n")
        f.write("=" * 80 + "\n\n")
        
        for dataset_name, results in all_results.items():
            f.write(f"\nDataset: {dataset_name}\n")
            f.write("=" * 80 + "\n")
            
            for result in results:
                f.write(f"\nConfiguration: {result['config']['name']}\n")
                f.write("-" * 40 + "\n")
                f.write(f"Best Loss: {result['best_loss']:.7f}\n")
                f.write(f"Best Epoch: {result['best_loss_epoch']}\n")
                f.write(f"Best Metrics:\n")
                f.write(f"  AEE: {result['best_metrics']['aee']:.4f}\n")
                f.write(f"  SDEE: {result['best_metrics']['sdee']:.4f}\n")
                f.write(f"  AAE: {result['best_metrics']['aae']:.4f}\n")
                f.write(f"  SDAE: {result['best_metrics']['sdae']:.4f}\n")
                f.write(f"Training Time: {result['training_time']:.2f} seconds\n")
                if result['time_to_best']:
                    f.write(f"Time to reach best epoch: {result['time_to_best']:.2f} seconds\n")
    
    main_logger.info("\nAll datasets processed successfully!")
    main_logger.info(f"Results saved in: {main_log_dir}")

if __name__ == "__main__":
    main() 